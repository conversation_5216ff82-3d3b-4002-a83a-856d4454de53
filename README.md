# WebHound - 网页URL提取工具

一个功能强大的Python桌面应用程序，用于从网页中提取和过滤URL链接，支持多URL处理、HTTP代理、多线程和多种导出格式。

## ✨ 核心功能

### 📝 多种URL输入方式
- **单个URL输入**：传统的单URL处理模式
- **多个URL输入**：支持在文本框中输入多个URL，每行一个
- **文件导入**：从TXT/CSV文件中批量导入URL列表
- **智能解析**：自动识别和解析各种URL格式

### 🚀 多线程并发处理
- **可配置线程数**：默认5线程，可在GUI中调整（1-20线程）
- **并发爬取**：同时处理多个URL，大幅提升处理速度
- **实时进度**：显示处理进度条和状态信息
- **错误处理**：单个URL失败不影响其他URL处理

### 🔧 高级配置选项
- **请求超时设置**：可配置HTTP请求超时时间（5-120秒）
- **User-Agent选择**：内置多种浏览器UA，支持自定义
- **自动去重**：智能去除重复的URL链接
- **批量验证**：一键验证所有输入URL的有效性

## 功能特性

### 🌐 HTTP代理服务器
- 内置HTTP代理服务器，支持HTTP和HTTPS协议
- 可配置代理端口（默认7890）
- 多线程处理，支持并发连接
- 实时状态监控

### 🔍 智能URL提取
- 从网页中提取所有URL链接
- 支持多种HTML标签：`<a>`, `<img>`, `<script>`, `<link>`
- 自动转换相对路径为绝对路径
- 智能过滤无效和重复链接

### 🎯 智能域名提取
- **域名提取模式**：去掉协议、路径、参数，只保留域名
- **保留子域名**：如 `https://news.catchideas.com/path` → `news.catchideas.com`
- **排除当前域名**：过滤掉源网站自身的链接
- **智能去重**：自动去除重复的域名

### 📊 多格式导出
- **TXT格式**：简洁的域名/URL列表
- **CSV格式**：包含序号、域名、协议等信息
- **UTF-8编码**：确保中文兼容性

### 🖥️ 友好的GUI界面
- 基于tkinter的现代化界面
- 实时状态显示和进度反馈
- 支持大量URL的滚动显示
- 直观的操作按钮和选项设置

## 🚀 快速开始

### 直接下载使用

1. 前往 [Releases](https://github.com/b6421582/WebHound/releases) 页面
2. 下载最新版本的 `WebHound.exe`
3. 双击运行，无需安装任何环境

## 使用说明

### 1. 启动代理服务器（可选）
- 在"HTTP代理服务器配置"区域设置端口
- 点击"启动代理"按钮
- 状态显示为绿色表示代理运行正常

### 2. 选择URL输入方式
- **单个URL**：在输入框中输入一个网页地址
- **多个URL**：在文本框中输入多个URL，每行一个
- **从文件导入**：点击"浏览"选择包含URL的TXT或CSV文件

### 3. 配置处理选项
- **排除当前域名的链接**：只保留外部域名的URL
- **使用代理进行爬取**：通过代理服务器访问目标网页
- **自动去重**：自动移除重复的URL
- **并发线程数**：设置同时处理的线程数量（1-20）
- **请求超时**：设置HTTP请求的超时时间（5-120秒）
- **User-Agent**：选择浏览器标识或自定义

### 4. 验证和处理
- 点击"验证URL"检查输入URL的有效性（可选）
- 点击"开始提取"开始批量处理
- 观察进度条和状态信息
- 如需中途停止，点击"停止"按钮

### 5. 查看结果
- 查看提取到的URL列表
- 点击"查看摘要"查看详细的处理报告
- 查看成功/失败统计信息

### 6. 导出结果
- **导出为TXT**：生成简单的URL列表
- **导出为CSV**：包含基本的URL信息
- **导出详细CSV**：包含完整的URL分析报告

## 🎯 应用场景

- **SEO分析**：快速收集竞争对手网站的外链域名
- **安全研究**：分析网页中的外部资源和链接
- **市场调研**：收集行业相关网站的域名信息
- **内容审核**：检查网页中的外部链接来源
- **数据分析**：批量处理网页链接，进行统计分析

## � 功能演示

### 域名提取示例
```
输入URL: https://sub.catchideas.com/path/to/page?param=value
输出域名: sub.catchideas.com
```

### 批量处理示例
```
输入:
- https://www.example.com/page1
- https://blog.example.com/post
- https://api.github.com/repos

输出:
- www.example.com
- blog.example.com
- api.github.com
```

## �🔧 技术特点

### 代理服务器
- 基于Python标准库实现
- 支持HTTP/HTTPS协议
- 多线程并发处理
- 自动端口冲突检测

### 域名提取算法
- BeautifulSoup HTML解析
- 智能相对路径处理
- 多标签URL提取（a、img、script、link等）
- 高效去重过滤

### 多线程架构
- ThreadPoolExecutor并发处理
- 实时进度回调
- 异常隔离处理
- 资源自动管理

## ❓ 常见问题

**Q: 代理服务器启动失败？**
A: 检查端口是否被占用，可以点击"自动端口"按钮选择可用端口。

**Q: 无法访问某些网站？**
A: 某些网站可能有反爬虫机制，可以尝试更换User-Agent或调整请求超时时间。

**Q: 提取的域名数量很少？**
A: 检查"排除当前域名"选项，某些页面的外部链接可能确实较少。

**Q: 如何批量处理多个URL？**
A: 可以选择"多个URL"输入方式，每行输入一个URL，或者使用"从文件导入"功能。

## 🛠️ 开发说明

WebHound采用模块化设计，主要包含以下组件：

- **核心模块**：负责HTTP代理、URL爬取和数据导出
- **GUI模块**：提供用户界面和交互逻辑
- **工具模块**：包含各种辅助函数

### 扩展功能
- 支持自定义过滤规则
- 可扩展新的导出格式
- 支持自定义User-Agent
- 可配置的代理功能

## ⭐ 支持项目

如果这个项目对您有帮助，请给它一个星标！

---

**WebHound** - 让域名提取变得简单高效！
